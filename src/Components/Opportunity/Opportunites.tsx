import React, { Dispatch, useEffect, useState, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useDetectClickOutside } from "react-detect-click-outside";
import { Helmet } from "react-helmet";

import {
  CREATE_OPPORTUNITY,
  HEADER_OPPORTUNITES,
  LIMIT,
  metaData,
  OPPORTUNITY,
  oppotunityItemPartialFetched,
  sectorItem,
  SKIP,
  subsectorItem,
  title,
} from "../constants";
import { getSector } from "../../store/actioncreators/sectoractions";
import {
  getSubSector,
  getSubSectorBySector,
} from "../../store/actioncreators/sub-sectoractions";
import OpportunityList from "./OpportunityList";
import opportunity_logo from "../../assests/images/opportunity_logo.png";
import { getQueryParams } from "../../utils";
import globe from "../../assests/home/<USER>";
import AdvancedSearch from "../Profile/AdvancedSearch";
import "./style.css";

const Opportunites = () => {
  const sectorlist: SECTOR = useSelector((state: STATE) => state.SECTOR.SECTOR);
  const subsectorlist: SUB_SECTOR = useSelector(
    (state: STATE) => state.SUB_SECTOR.SUB_SECTOR
  );
  const opportunites: OPP = useSelector((state: STATE) => state.OPP.OPP);
  const dispatch: Dispatch<any> = useDispatch();
  const navigator = useNavigate();
  const [maxSkip, setMaxSkip] = useState(0);

  const [sector, setSector] = useState({
    drop: false,
    selected: "",
    id: "",
  });

  const [subSector, setSubSector] = useState({
    drop: false,
    selected: "",
    id: "",
    count: 0,
  });

  const [page, setPage] = useState({
    skip: getQueryParams("skip") ? getQueryParams("skip") : SKIP,
    limit: LIMIT,
  });

  const ref1 = useDetectClickOutside({
    onTriggered: () => {
      setSector({ ...sector, drop: false });
    },
  });

  const ref2 = useDetectClickOutside({
    onTriggered: () =>
      setSubSector({
        drop: false,
        selected: "Select sub sector",
        id: "",
        count: 0,
      }),
  });

  const fetchData = (value: number) => {
    let final = +page.skip + value < 0 ? +page.skip : +page.skip + value;
    setPage({ skip: final.toString(), limit: page.limit });
    navigator(OPPORTUNITY + `?skip=${final}`);
    window.scrollTo(0, 0);
  };

  useEffect(() => {
    if (opportunites?.OPP_LIST?.opportunitiesCount)
      setMaxSkip(
        Math.ceil(opportunites.OPP_LIST.opportunitiesCount / parseInt(LIMIT))
      );
  }, [page, opportunites]);

  useEffect(() => {
    dispatch(getSector());
    dispatch(getSubSector());
    setSector({
      ...sector,
      selected: "All Sectors",
      id: "",
    });
    setSubSector({
      ...subSector,
      selected: "All Sub-Sectors",
      id: "",
      count: 0,
    });
    console.log({ maxSkip });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSectorClick = (id: string, name: string) => {
    if (id !== sector.id || sector.id == "") {
      dispatch(getSubSectorBySector(id));
      setSector({ ...sector, id: id, selected: name });
      setSubSector({ ...subSector, drop: true });
    } else {
      setSubSector({ ...subSector, drop: !subSector.drop });
    }
  };

  const handleSubSectorClick = () => {};

  const getCountbyFilter = () => {
    let count =
      opportunites.OPP_LIST?.opportunities &&
      opportunites.OPP_LIST?.opportunities?.reduce(
        (count: number, item: oppotunityItemPartialFetched) =>
          count + Number(item.subSectorId === subSector.id),
        0
      );
    return count;
  };

  const [search, setSearch] = useState("");
  const [useAdvancedSearch, setUseAdvancedSearch] = useState(false);
  const [filteredResults, setFilteredResults] = useState<any[]>([]);

  const handleSearch = (search: string) => {
    setPage({ skip: "0", limit: LIMIT });
    setSearch(search);
  };

  // Transform opportunities data for AdvancedSearch component
  const searchableOpportunities = useMemo(() => {
    if (!opportunites?.OPP_LIST?.opportunities) return [];

    return opportunites.OPP_LIST.opportunities.map((item: any) => ({
      id: item.id || item._id,
      title: item.name,
      description: item.description || item.technologyPartnerRequirement || "",
      category: item.sectorId || "Business",
      status: item.status || "Active",
      date: new Date(item.createdAt || Date.now()),
      tags: [
        item.sectorId,
        item.subSectorId,
        item.company?.name,
        ...(item.keywords || []),
        ...(item.technologies || []),
      ].filter(Boolean),
      originalData: item,
    }));
  }, [opportunites]);

  // Categories for filtering
  const categories = useMemo(() => {
    const sectors = sectorlist?.SECTOR_LIST || [];
    return sectors.map((sector: any) => ({
      value: sector.id,
      label: sector.name,
    }));
  }, [sectorlist]);

  // Status options for filtering
  const statuses = [
    { value: "Active", label: "Active" },
    { value: "Pending", label: "Pending" },
    { value: "Closed", label: "Closed" },
    { value: "Draft", label: "Draft" },
  ];

  // Available tags for filtering
  const availableTags = useMemo(() => {
    const allTags = searchableOpportunities.flatMap((item) => item.tags);
    return Array.from(new Set(allTags)).sort();
  }, [searchableOpportunities]);

  const handleSearchResults = (results: any[]) => {
    setFilteredResults(results);
  };

  // Initialize filtered results when searchable opportunities change
  useEffect(() => {
    if (useAdvancedSearch) {
      setFilteredResults(searchableOpportunities);
    }
  }, [searchableOpportunities, useAdvancedSearch]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Helmet>
        <title>{title.OPPORTUNITY}</title>
        <meta
          name="description"
          key="description"
          content={metaData.OPPORTUNITY}
        />
        <meta name="title" key="title" content={HEADER_OPPORTUNITES} />
        <meta property="og:title" content={HEADER_OPPORTUNITES} />
        <meta property="og:description" content={HEADER_OPPORTUNITES} />
        <meta property="og:image" content={globe} />
        <meta
          property="og:url"
          content={`${process.env.REACT_APP_BASE_URL}/opportunities`}
        />
        <meta property="og:type" content="website" />
        <meta name="twitter:title" content={HEADER_OPPORTUNITES} />
        <meta name="twitter:description" content={metaData.OPPORTUNITY} />
        <meta name="twitter:image" content={globe} />
        <meta name="twitter:card" content={metaData.OPPORTUNITY} />
      </Helmet>
      {/* Enhanced Hero Header Section */}
      <div className="relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-800">
          <div
            className="absolute inset-0 opacity-20"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            }}
          ></div>
        </div>

        {/* Floating Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-white/5 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-indigo-300/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        </div>

        <div className="relative w-full px-4 sm:px-6 lg:px-8 py-16 sm:py-20 lg:py-24">
          <div className="text-center">
            {/* Animated Badge */}
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white/90 text-sm font-medium mb-6 animate-fadeInUp">
              <svg
                className="w-4 h-4 mr-2"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
              </svg>
              Market Opportunities
            </div>

            {/* Main Title with Gradient */}
            <h1 className="text-5xl sm:text-6xl lg:text-7xl font-bold text-white mb-6 animate-fadeInUp delay-200">
              <span className="bg-gradient-to-r from-white via-blue-100 to-indigo-100 bg-clip-text text-transparent">
                Discover Opportunities
              </span>
            </h1>

            {/* Enhanced Description */}
            <p className="text-xl sm:text-2xl text-blue-100 max-w-4xl mx-auto mb-8 leading-relaxed animate-fadeInUp delay-300">
              Explore partnership opportunities and market collaborations from
              around the world in our global business ecosystem.
            </p>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center animate-fadeInUp delay-500">
              <button
                onClick={() => navigator(CREATE_OPPORTUNITY)}
                className="group relative px-8 py-4 bg-white text-blue-600 font-semibold rounded-2xl hover:bg-blue-50 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-2xl"
              >
                <span className="flex items-center gap-2">
                  <svg
                    className="w-5 h-5 group-hover:rotate-90 transition-transform duration-300"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 4v16m8-8H4"
                    />
                  </svg>
                  Create New Opportunity
                </span>
              </button>

              <button className="group px-8 py-4 border-2 border-white/30 text-white font-semibold rounded-2xl hover:bg-white/10 backdrop-blur-sm transition-all duration-300">
                <span className="flex items-center gap-2">
                  <svg
                    className="w-5 h-5 group-hover:scale-110 transition-transform duration-300"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  Learn More
                </span>
              </button>
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="relative bg-white/10 backdrop-blur-sm border-t border-white/20">
          <div className="w-full px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex justify-center items-center space-x-12">
              <div className="text-center">
                <div className="text-3xl font-bold text-white">
                  {opportunites.TOTAL || 0}
                </div>
                <div className="text-sm text-blue-100">Opportunities</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white">
                  {sectorlist?.SECTOR_LIST?.length || 0}
                </div>
                <div className="text-sm text-blue-100">Sectors</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Filters and Search Section */}
      <div className="w-full max-w-none mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
          {/* Filter Header with Sort and Advanced Options */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 space-y-4 sm:space-y-0">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <svg
                  className="w-5 h-5 text-gray-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
                  />
                </svg>
                <span className="text-lg font-semibold text-gray-900">
                  Filters & Search
                </span>
              </div>

              {/* Results Count with Analytics */}
              <div className="flex items-center space-x-4">
                <div className="text-sm text-gray-600">
                  {opportunites.TOTAL || 0} opportunities found
                </div>
                {search && (
                  <div className="text-xs text-GTI-BLUE-default bg-blue-50 px-2 py-1 rounded-full">
                    Searching for: "{search}"
                  </div>
                )}
              </div>
            </div>

            {/* Sort and Clear Filters */}
            <div className="flex items-center space-x-3">
              {/* Sort Dropdown */}
              <div className="relative">
                <select className="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-GTI-BLUE-default focus:border-GTI-BLUE-default">
                  <option value="newest">Newest First</option>
                  <option value="oldest">Oldest First</option>
                  <option value="relevance">Most Relevant</option>
                </select>
                <svg
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </div>

              {/* Clear All Filters */}
              {(sector.id !== "" || subSector.id !== "" || search) && (
                <button
                  onClick={() => {
                    setSector({ id: "", drop: false, selected: "All Sectors" });
                    setSubSector({
                      ...subSector,
                      id: "",
                      drop: false,
                      count: 0,
                    });
                    setSearch("");
                  }}
                  className="flex items-center space-x-1 px-3 py-2 text-sm text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors"
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                  <span>Clear All</span>
                </button>
              )}
            </div>
          </div>

          {/* Main Filter Bar */}
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Primary Filters */}
            <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
              {/* Sector Filter */}
              <div className="relative" ref={ref1}>
                <button
                  type="button"
                  className="flex items-center justify-between w-full sm:w-48 px-4 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-GTI-BLUE-default focus:border-GTI-BLUE-default transition-all duration-200"
                  onClick={() => {
                    setSector({ ...sector, drop: !sector.drop });
                    setSubSector({ ...subSector, drop: false });
                  }}
                  aria-expanded={sector.drop}
                  aria-haspopup="true"
                  aria-label="Select sector filter"
                  id="sector-filter-button"
                >
                  <span className="truncate">
                    {sector.id === "" ? "All Sectors" : sector.selected}
                  </span>
                  <svg
                    className={`w-4 h-4 transition-transform duration-200 ${
                      sector.drop ? "rotate-180" : ""
                    }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </button>
                {sector.drop && (
                  <div
                    className="absolute top-full left-0 mt-2 w-full bg-white border border-gray-200 rounded-xl shadow-lg z-20 max-h-60 overflow-auto"
                    role="listbox"
                    aria-labelledby="sector-filter-button"
                  >
                    <div
                      className="block px-4 py-3 text-sm text-gray-700 hover:bg-GTI-BLUE-default/10 hover:text-GTI-BLUE-default transition-colors duration-150 cursor-pointer border-b border-gray-50"
                      role="option"
                      tabIndex={0}
                      onClick={() => {
                        setSector({
                          id: "",
                          drop: false,
                          selected: "All Sectors",
                        });
                        setSubSector({
                          ...subSector,
                          id: "",
                          drop: false,
                          count: 0,
                        });
                      }}
                      onKeyDown={(e) => {
                        if (e.key === "Enter" || e.key === " ") {
                          e.preventDefault();
                          setSector({
                            id: "",
                            drop: false,
                            selected: "All Sectors",
                          });
                          setSubSector({
                            ...subSector,
                            id: "",
                            drop: false,
                            count: 0,
                          });
                        }
                      }}
                    >
                      All Sectors
                    </div>
                    {sectorlist &&
                      sectorlist.SECTOR_LIST.map((item: sectorItem, id) => {
                        return (
                          <div
                            key={id}
                            className="block px-4 py-3 text-sm text-gray-700 hover:bg-GTI-BLUE-default/10 hover:text-GTI-BLUE-default transition-colors duration-150 cursor-pointer border-b border-gray-50 last:border-b-0"
                            role="option"
                            tabIndex={0}
                            onClick={() => {
                              handleSectorClick(item._id, item.name);
                            }}
                            onKeyDown={(e) => {
                              if (e.key === "Enter" || e.key === " ") {
                                e.preventDefault();
                                handleSectorClick(item._id, item.name);
                              }
                            }}
                          >
                            {item.name}
                          </div>
                        );
                      })}
                  </div>
                )}
              </div>

              {/* Subsector Filter */}
              {subsectorlist.SUB_SECTOR_LIST.length > 0 &&
              sector.selected !== "All Sectors" &&
              sector.id !== "" ? (
                <div className="relative" ref={ref2}>
                  <button
                    type="button"
                    className="flex items-center justify-between w-full sm:w-48 px-4 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-GTI-BLUE-default focus:border-GTI-BLUE-default transition-all duration-200"
                    onClick={() => {
                      setSector({ ...sector, drop: false });
                      setSubSector((prev) => ({ ...prev, drop: !prev.drop }));
                    }}
                    aria-expanded={subSector.drop}
                    aria-haspopup="true"
                    aria-label="Select subsector filter"
                    id="subsector-filter-button"
                  >
                    <span className="truncate">
                      {sector.id === ""
                        ? "Select Subsector"
                        : subSector.selected}
                    </span>
                    <svg
                      className={`w-4 h-4 transition-transform duration-200 ${
                        subSector.drop ? "rotate-180" : ""
                      }`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </button>
                  {subSector.drop && (
                    <div
                      className="modern-dropdown-content"
                      role="listbox"
                      aria-labelledby="subsector-filter-button"
                    >
                      {subsectorlist.SUB_SECTOR_LIST.length > 0 &&
                        subsectorlist.SUB_SECTOR_LIST.map(
                          (item: subsectorItem, id) => {
                            return (
                              <div
                                key={id}
                                className="modern-dropdown-item"
                                role="option"
                                tabIndex={0}
                                onClick={() => {
                                  setSubSector({
                                    id: item._id,
                                    drop: false,
                                    selected: item.name,
                                    count: getCountbyFilter(),
                                  });
                                  handleSubSectorClick();
                                }}
                                onKeyDown={(e) => {
                                  if (e.key === "Enter" || e.key === " ") {
                                    e.preventDefault();
                                    setSubSector({
                                      id: item._id,
                                      drop: false,
                                      selected: item.name,
                                      count: getCountbyFilter(),
                                    });
                                    handleSubSectorClick();
                                  }
                                }}
                              >
                                {item.name}
                              </div>
                            );
                          }
                        )}
                    </div>
                  )}
                </div>
              ) : null}
            </div>

            {/* Search Input with Advanced Toggle */}
            <div className="flex-1 max-w-md">
              <div className="flex items-center gap-2 mb-3">
                <button
                  onClick={() => setUseAdvancedSearch(!useAdvancedSearch)}
                  className={`px-3 py-1 text-xs rounded-full transition-colors duration-200 ${
                    useAdvancedSearch
                      ? "bg-GTI-BLUE-default text-white"
                      : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                  }`}
                >
                  {useAdvancedSearch ? "Advanced Search" : "Basic Search"}
                </button>
                <span className="text-xs text-gray-500">
                  {useAdvancedSearch ? "Switch to basic" : "Switch to advanced"}
                </span>
              </div>

              {!useAdvancedSearch ? (
                <div className="relative">
                  <label htmlFor="opportunity-search" className="sr-only">
                    Search opportunities
                  </label>
                  <svg
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                  <input
                    type="text"
                    id="opportunity-search"
                    className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-xl text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default transition-all duration-200 bg-white shadow-sm"
                    placeholder="Search opportunities..."
                    value={search}
                    onChange={(e) => {
                      handleSearch(e.target.value);
                    }}
                    aria-describedby="search-help"
                  />
                  <div id="search-help" className="sr-only">
                    Search through available market opportunities and
                    partnerships
                  </div>
                </div>
              ) : (
                <div className="mt-4">
                  <AdvancedSearch
                    items={searchableOpportunities}
                    onResultsChange={handleSearchResults}
                    placeholder="Search opportunities by name, description, or partnership requirements..."
                    categories={categories}
                    statuses={statuses}
                    availableTags={availableTags}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Opportunities List */}
      <OpportunityList
        skip={page.skip}
        limit={LIMIT}
        secId={sector.id}
        subSecId={subSector.id}
        search={search}
      />

      {/* Modern Pagination */}
      <div className="w-full max-w-none mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <nav
          aria-label="Opportunities pagination"
          className="flex justify-center items-center space-x-2 border-t border-gray-100 pt-8"
        >
          <button
            disabled={page.skip === "0"}
            onClick={() => {
              fetchData(-1);
            }}
            className={`button ${
              page.skip === "0"
                ? "not-active opacity-50 cursor-not-allowed"
                : "not-active hover:bg-GTI-BLUE-default hover:text-white"
            }`}
            aria-label="Go to previous page"
          >
            <svg
              className="w-4 h-4 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M15 19l-7-7 7-7"
              />
            </svg>
            Previous
          </button>

          <span className="text-sm text-gray-600 px-4" aria-live="polite">
            Page {Math.floor(+page.skip / +LIMIT) + 1} of {maxSkip}
          </span>

          <button
            disabled={+page.skip + 1 >= maxSkip}
            onClick={() => {
              fetchData(1);
            }}
            className={`button ${
              +page.skip + 1 >= maxSkip
                ? "not-active opacity-50 cursor-not-allowed"
                : "not-active hover:bg-GTI-BLUE-default hover:text-white"
            }`}
            aria-label="Go to next page"
          >
            Next
            <svg
              className="w-4 h-4 ml-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M9 5l7 7-7 7"
              />
            </svg>
          </button>
        </nav>
      </div>
    </div>
  );
};
export default Opportunites;
